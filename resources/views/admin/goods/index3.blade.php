@extends('layouts.admin')

@section('modal')
    <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="actionModalLabel">გზავნილის დეკლარაცია</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <form class="form" id="kt_form" action="{{route("declaration.update3")}}"
                          enctype="multipart/form-data" method="POST" >
                        @csrf
                        <input type="hidden" name="good_id" id="good_id">
                        <div class="form-group col-12 geo">
                            <x-text-input label="თრექინგ კოდი" placeholder="თრექინგ კოდი 3847544030345"
                                          name="tracking_code" type="text" value=""/>
                        </div>
                        <div class="form-group col-12 geo">
                            <label>გზავნილის ტიპი</label>
                            <div class="radio-inline">
                                <label class="radio" id="shop">
                                    <input type="radio" name="parcel_type"/>
                                    <span></span>
                                    ონლაინ მაღაზია
                                </label>
                                <label class="radio" id="personal">
                                    <input type="radio" name="parcel_type"/>
                                    <span></span>
                                    პირადი გზავნილი
                                </label>
                            </div>
                        </div>
                        <div class="form-group col-12 geo" id="shop_name">
                            <label>ონლაინ მაღაზია</label>
                            <input type="text" class="form-control" name="sender_company"
                                   id="sender_company"
                                   placeholder="taobao.com"
                                   value="taobao.com"/>
                        </div>
                        <div class="form-group col-12 geo">
                            <label class="font-size-h6 font-weight-bolder text-dark">ამანათის კატეგორია (კოდი)</label>
                            <div
                                    class="dropdown bootstrap-select show form-control form-control-lg  form-control-solid py-4 px-4 border-0 rounded-lg font-size-h6 ">
                                <select class="form-control selectpicker" data-size="7" data-live-search="true"
                                        tabindex="null" name="category_id" id="category_id">
                                    <option value="">-- აირჩიეთ ამანათის კატეგორია --</option>
                                    @foreach($categories as $itemcat)
                                        <option value="{{$itemcat->id}}">{{$itemcat->code}} - {{$itemcat->description_ge}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-12 geo">
                            <div class="form-group">
                                <label>ამანათის ღირებულება</label>
                                <input type="number" step="0.01" class="form-control" placeholder="20" name="client_buy_amount"
                                       id="client_buy_amount"
                                       value=""/>
                            </div>
                            {{--                            suratebis saxeli--}}
                            <label class="text-danger">300 ლარზე მეტი ღირებულების ამანათისთვის შესყიდვის ინვოისის ატვირთვა სავალდებულოა</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input"
                                       id="cover_image" name="cover_image">
                                <label id="cover_image2" class="custom-file-label text-muted" for="cover_image">ატვირთეთ ინვოისი</label>
                            </div>
                        </div>
                        <p>
                            <span class="navi-icon">
                                <i class="la la-print"></i>
                            </span>

                            <a id="cover_image3" href="/storage/invoice_images/supra_1611838557.jpg" target="_blank" rel="noopener noreferrer">
                                ინვოისი
                            </a>
                        </p>

                        <div class="form-group row">
                            <label class="col-xl-3 col-lg-3 col-form-label text-right geo">
                                ექვემდებარება განბაჟებას
                            </label>
                            <div class="col-3">
                                    <span class="switch switch-lg switch-icon">
                                        <label>
                                            <input type="checkbox"
                                                   {{--                                                               {{ $user->user_wants_sms === 1 ? 'checked' : '' }}--}}
                                                   value="1"
                                                   name="customize" id="customize">
                                            <span></span>
                                        </label>
                                    </span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">დახურვა</button>
                            <button type="submit" class="btn btn-primary">ცვლილებების შენახვა</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column-fluid" style="margin-top:-70px">
            <div class=" container ">
                <div class="card card-custom" style="margin-top:2rem">
                    <div class="card-body">
                        <div class="mb-70">

                            <h1 style="font-weight: bold;">ამანათები V3</h1>
                            <br>
                            <!-- Per page selector -->
                            <div class="form-group">
                                <label for="perPage">ჩანაწერები გვერდზე:</label>
                                <select id="perPage" class="form-control form-control-sm" style="width: auto; display: inline;">
                                    <option value="5">5</option>
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label style="font-weight: bold;">შეიყვანეთ თრექინგ კოდი</label>
                                <input type="text" id="tracking-code-input" class="form-control" placeholder="შეიყვანეთ თრექინგ კოდი...">
                            </div>

                            <!-- Error Message -->
                            <div id="error-message" class="alert alert-danger d-none"></div>

                            <!-- Tracking Information (hidden initially) -->
                            <div id="tracking-info" class="mt-4 d-none">
                                <p class="text-dark" id="comment"></p>
                                <p class="text-dark" id="tracking-code"></p>
                                <p class="text-dark" id="rec-name"></p>
                                <p class="text-dark" id="room-number"></p>
                                <p class="text-dark" id="branch-name"></p>
                                <p class="text-dark" id="flight-number"></p>
                                <p class="text-dark" id="weight"></p>
                                <p class="text-dark" id="parcel-count"></p>
                                <p class="text-dark" id="total-parcels"></p>
                                <p id="customs-status"></p>
                            </div>

                            <div class="form-group">
                                <button id="clear-filters" class="btn btn-secondary">ფილტრების გასუფთავება</button>
                                <button id="export-btn" class="btn btn-primary">ექსპორტი</button>

                            </div>
                            <div class="table-responsive" style="overflow-x: auto;">
                                <table id="data-table" class="table table-bordered table-striped">
                                <thead>
                                <tr style="background-color: #64b1ff">
                                    <th class="sortable" data-column="tracking_code" style="text-align: center;">
                                        <div class="font-weight-bold"  style="font-size: 16px; color: #333; cursor: pointer;">
                                            კოდი
                                            <span class="sort-icon"></span>
                                        </div>

                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-tracking-code"
                                                    class="form-control form-control-sm"
                                                    placeholder="ძებნა თრექინგ კოდით"
                                                    style="width: 180px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>



                                    <th class="sortable" data-column="rec_name" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333; cursor: pointer;">
                                            მომხმარებელი
                                            <span class="sort-icon"></span>
                                        </div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-rec_name"
                                                    class="form-control form-control-sm"
                                                    placeholder="ძებნა მომხმარებლით"
                                                    style="width: 180px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="room_number" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333; cursor: pointer;">ოთახის ნომერი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-room_number"
                                                    class="form-control form-control-sm"
                                                    placeholder="ძებნა ოთახის ნომრით"
                                                    style="width: 180px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="flight.flight_number" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">ფრენის ნომერი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-flight_number"
                                                    class="form-control form-control-sm"
                                                    placeholder="ძებნა ფრენის ნომრით"
                                                    style="width: 140px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="small_comment" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">სკანირების სტატუსი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-small_comment"
                                                    class="form-control form-control-sm"
                                                    style="width: 70px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="SCANNED">სკანირებული</option>
                                                <option value="null">არცერთი</option>
                                            </select>
                                        </div>
                                    </th>


                                    <th class="sortable" data-column="flight.takeout_date" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">ფრენის თარიღი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="date"
                                                    id="start-takeout_date"
                                                    class="form-control form-control-sm"
                                                    placeholder="დაწყების თარიღი"
                                                    style="width: 130px; border-radius: 5px; border: 1px solid #ccc; padding: 5px; margin-bottom: 5px;">
                                            <input
                                                    type="date"
                                                    id="end-takeout_date"
                                                    class="form-control form-control-sm"
                                                    placeholder="დასრულების თარიღი"
                                                    style="width: 130px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>


                                    <th class="sortable" data-column="phisicalw" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">წონა<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-phisicalw"
                                                    class="form-control form-control-sm"
                                                    placeholder="ძებნა წონით"
                                                    style="width: 80px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="price_to_pay" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">თანხა<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <input
                                                    type="text"
                                                    id="search-price_to_pay"
                                                    class="form-control form-control-sm"
                                                    placeholder="Search by Price"
                                                    style="width: 100px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="flight_parcel_state" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">ამანათის სტატუსი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-flight_parcel_state"
                                                    class="form-control form-control-sm"
                                                    style="width: 100px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="RECIEVED">ჩამოსული</option>
                                                <option value="TOOK_OUT">გატანილი</option>
                                                <option value="SENT">გამოგზავნილი</option>
                                                <option value="WAITING">მისაღები</option>
                                                <option value="SUSPENDED">გაჩერებული</option>
                                                <option value="NONE">არცერთი</option>
                                            </select>
                                        </div>
                                    </th>


                                    <th class="sortable" data-column="is_payed" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">გადახდა<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-is_payed"
                                                    class="form-control form-control-sm"
                                                    style="width: 50px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="1">კი</option>
                                                <option value="0">არა</option>
                                            </select>
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="is_declared" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">დეკლარაცია<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-is_declared"
                                                    class="form-control form-control-sm"
                                                    style="width: 50px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="1">კი</option>
                                                <option value="0">არა</option>
                                            </select>
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="must_customize" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">განბაჟება<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-must_customize"
                                                    class="form-control form-control-sm"
                                                    style="width: 50px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="1">კი</option>
                                                <option value="0">არა</option>
                                            </select>
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="uses_courier" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">საკურიერო<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-uses_courier"
                                                    class="form-control form-control-sm"
                                                    style="width: 50px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="1">კი</option>
                                                <option value="0">არა</option>
                                            </select>
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="admin_comment" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">ადმინის კომენტარი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-admin_comment"
                                                    class="form-control form-control-sm"
                                                    style="width: 50px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>
                                                <option value="1">კი</option>
                                                <option value="0">არა</option>
                                            </select>
                                        </div>
                                    </th>

                                    <th class="sortable" data-column="branch_id" style="text-align: center;">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;cursor: pointer;">ფილიალი<span class="sort-icon"></span></div>
                                        <div class="form-group" style="margin-top: 8px;">
                                            <select
                                                    id="search-branch_id"
                                                    class="form-control form-control-sm"
                                                    style="width: 180px; border-radius: 5px; border: 1px solid #ccc; padding: 5px;">
                                                <option value="">ყველა</option>

                                                @foreach($branches as $branch)

                                                    <option value="<?= htmlspecialchars($branch['id']) ?>">
                                                            <?= htmlspecialchars($branch['title_ge']) ?>
                                                    </option>
                                                @endforeach

                                            </select>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="branch_id" style="text-align: center; width: 200px">
                                        <div class="font-weight-bold" style="font-size: 16px; color: #333;">მოქმედებები</div>
                                        <br>
                                        <div class="form-group" style="margin-top: 8px; width: 250px">

                                        </div>
                                    </th>

                                    <!-- Add other column headers as needed -->
                                </tr>
                                </thead>
                                <tbody id="table-body">
                                <!-- Data will be dynamically loaded here -->
                                </tbody>
                            </table>
                            </div>
                            <div id="pagination">
                                <!-- Pagination links will be dynamically loaded here -->
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section("scripts")
    <style>
        .sort-icon {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-left: 5px;
            vertical-align: middle;
            background-size: contain;
            background-repeat: no-repeat;
            visibility: hidden; /* Hidden by default */
        }

        .sortable.asc .sort-icon {
            visibility: visible;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTBweCIgaGVpZ2h0PSIxMHB4IiB2aWV3Qm94PSIwIDAgMTAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNSA1M2wzNSA0M2gtNzBMMDUgNTN6IiBmaWxsPSIjMzMzIi8+PC9zdmc+');
        }

        .sortable.desc .sort-icon {
            visibility: visible;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTBweCIgaGVpZ2h0PSIxMHB4IiB2aWV3Qm94PSIwIDAgMTAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNSA0MmwzNSA0M2gtNzBMNSA0MnoiIGZpbGw9IiMzMzMiLz48L3N2Zz4=');
        }

    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>

        document.getElementById('export-btn').addEventListener('click', function () {

            let searchData = {
                id_min: $('#search-id-min').val(),
                id_max: $('#search-id-max').val(),
                tracking_code: $('#search-tracking-code').val(),
                rec_name: $('#search-rec_name').val(),
                room_number: $('#search-room_number').val(),
                flight_number: $('#search-flight_number').val(),
                start_takeout_date: $('#start-takeout_date').val(),
                end_takeout_date: $('#end-takeout_date').val(),
                phisicalw: $('#search-phisicalw').val(),
                price_to_pay: $('#search-price_to_pay').val(),
                small_comment: $('#search-small_comment').val(),
                flight_parcel_state: $('#search-flight_parcel_state').val(),
                is_payed: $('#search-is_payed').val(),
                is_declared: $('#search-is_declared').val(),
                must_customize: $('#search-must_customize').val(),
                uses_courier: $('#search-uses_courier').val(),
                branch_id: $('#search-branch_id').val(),
                admin_comment: $('#search-admin_comment').val(),
            };


            // API endpoint that returns the Excel file
            const url = '{{ route('goods.export') }}';

            // Make an API call to fetch the file
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({search: searchData})
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to download file');
                    }
                    return response.blob(); // Get the binary data as a Blob
                })
                .then(blob => {
                    // Create a URL for the binary data
                    const url = window.URL.createObjectURL(blob);

                    // Create a temporary anchor element to trigger the download
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'exported-data.xlsx'; // Set the file name
                    document.body.appendChild(a); // Append the anchor to the document
                    a.click(); // Trigger the download
                    a.remove(); // Clean up the anchor
                    window.URL.revokeObjectURL(url); // Release the blob URL
                })
                .catch(error => {
                    console.error('Error during export:', error);
                    alert('ფაილის ექსპორტი ვერ მოხერხდა. გთხოვთ სცადოთ ხელახლა.');
                });
        });

        function debounce(func, delay) {
            let timer;
            return function (...args) {
                const context = this;
                clearTimeout(timer);
                timer = setTimeout(() => func.apply(context, args), delay);
            };
        }

        document.getElementById('tracking-code-input').addEventListener(
            'input',
            debounce(function () {
                const trackingCode = this.value;

                if (trackingCode.trim() === '') {
                    // If the input is empty, hide the tracking information
                    document.getElementById('tracking-info').classList.add('d-none');
                    document.getElementById('error-message').classList.add('d-none');
                    return;
                }

                // API Request to get tracking info
                fetch(`/admin/tracking/${trackingCode}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            this.value = '';

                            // If the API returns an error
                            document.getElementById('error-message').innerText = data.error;
                            document.getElementById('error-message').classList.remove('d-none');
                            document.getElementById('tracking-info').classList.add('d-none');
                        } else {
                            this.value = '';

                            // If the tracking code is found, populate the information
                            document.getElementById('tracking-code').innerText = `თრექინგ კოდი: ${data.tracking_code}`;

                            document.getElementById('comment').innerHTML = `ადმინისტრაციის კომენტარი: <span style="color: red;">${data.admin_comment}</span>`;
                            document.getElementById('comment').style.fontWeight = 'bold';



                            document.getElementById('rec-name').innerText = `მიმღების სახელი: ${data.rec_name}`;
                            document.getElementById('room-number').innerText = `ოთახის ნომერი: ${data.room_number}`;
                            document.getElementById('branch-name').innerText = `ფილიალი: ${data.branch_name}`;
                            document.getElementById('flight-number').innerText = `ფრენის ნომერი: ${data.flight_number}`;
                            document.getElementById('weight').innerText = `წონა: ${data.weight}`;
                            document.getElementById('parcel-count').innerText = `ამანათების რაოდენობა: ${data.user_parcel_count}`;
                            document.getElementById('total-parcels').innerText = `სულ ამანათები: ${data.total_parcels}`;
                            const customsStatus = data.customs_clearance ? 'განბაჟებული' : 'არ არის განბაჟებული';
                            document.getElementById('customs-status').innerText = customsStatus;

                            // Show the tracking information
                            document.getElementById('tracking-info').classList.remove('d-none');
                            document.getElementById('error-message').classList.add('d-none');
                        }
                    })
                    .catch(error => {
                        // Handle network or other errors
                        document.getElementById('error-message').innerText =
                            'მონაცემების მიღებისას მოხდა შეცდომა.';
                        document.getElementById('error-message').classList.remove('d-none');
                    });
            }, 300) // Adjust the delay (300ms) as needed
        );




        $(document).on("click", ".declaration_button", function () {
            let good_id = $(this).data("id");

            $.post('{{route("parcels.find")}}', {
                '_token': $('meta[name=csrf-token]').attr('content'),
                id: good_id
            }, function (data) {
                $("#tracking_code").val(data.tracking_code);
                if (data.sender_company !== '') {
                    $("#shop").prop('checked', true).trigger("click");
                    $("#sender_company").val(data.sender_company);
                } else {
                    $("#personal").prop('checked', true).trigger("click");
                }

                if (data.sender_company !== '') {
                    $("#customize").prop('checked', true);
                } else {
                    $("#customize").prop('checked', false);
                }

                $("#category_id").val(data.category_id).change();
                $("#client_buy_amount").val(data.client_buy_amount);
                $("#cover_image2").text(data.cover_image);
                $("#cover_image3").text(data.cover_image)
                    .attr("href", "/storage/invoice_images/" + data.cover_image);

            });

            // Set hidden good ID
            $("#good_id").val(good_id);
        });



        $(document).ready(function() {
            console.log('Document ready, jQuery loaded'); // Debug

            // Test if jQuery is working
            $('body').on('click', function() {
                console.log('Body clicked - jQuery is working!');
            });
            // Function to get URL parameter
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // Function to update URL without page reload
            function updateUrl(page) {
                const url = new URL(window.location);
                if (page > 1) {
                    url.searchParams.set('page', page);
                } else {
                    url.searchParams.delete('page');
                }
                window.history.pushState({}, '', url);
            }

            // Function to load table data via AJAX
            function loadData(page = 1, sortColumn = 'id', sortDirection = 'asc', searchData = {}, perPage = 10, updateUrlFlag = true) {
                console.log('loadData called with page:', page); // Debug

                $.ajax({
                    url: '{{ route("goods.index3.data") }}',
                    type: 'GET',
                    data: {
                        page: page,
                        per_page: perPage,  // Pass perPage parameter
                        sort_column: sortColumn,
                        sort_direction: sortDirection,
                        search: searchData
                    },
                    success: function(response) {
                        console.log('AJAX success, response:', response); // Debug

                        // Update URL if needed
                        if (updateUrlFlag) {
                            updateUrl(page);
                        }

                        // Update table body with new data
                        $('#table-body').html('');
                        $.each(response.data.data, function(index, item) {
                            $('#table-body').append(`
                                <tr>
                                    <td><a href="admin/parcels/edit/${item.tracking_code}" class="edit-link">${item.tracking_code}</a></td>
                                    <td>${item.rec_name}</td>
                                    <td>${item.room_number}</td>
                                    <td>${item.flight ? item.flight_number : '-'}</td>
                                    <td>${item.small_comment}</td>
                                    <td>${item.flight ? item.takeout_date : '-'}</td>
                                    <td>${item.phisicalw}</td>
                                    <td>${item.price_to_pay}</td>
                                    <td>${item.flight_parcel_state}</td>
                                    <td>${item.is_payed ? '<svg class="h-5 w-5 stroke-current text-green-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : '<svg class="h-5 w-5 stroke-current text-red-300 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'}</td>
                                    <td>${item.is_declared ? '<svg class="h-5 w-5 stroke-current text-green-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : '<svg class="h-5 w-5 stroke-current text-red-300 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'}</td>
                                    <td>${item.must_customize ? '<svg class="h-5 w-5 stroke-current text-green-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : '<svg class="h-5 w-5 stroke-current text-red-300 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'}</td>
                                    <td>${item.uses_courier ? '<svg class="h-5 w-5 stroke-current text-green-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : '<svg class="h-5 w-5 stroke-current text-red-300 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'}</td>
                                    <td>${item.admin_comment ? '<svg class="h-5 w-5 stroke-current text-green-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' : '<svg class="h-5 w-5 stroke-current text-red-300 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'}</td>
                                    <td>${item.branch.title_en}</td>
                                    <td>
                                       <button class="btn btn-danger btn-sm delete-btn" data-id="${item.id}" data-url="{{ route('parcels.delete', ['goods' => 'ID_PLACEHOLDER']) }}">წაშლა</button>
                                        <button class="btn btn-info btn-sm declaration_button" data-id="${item.id}" data-bs-toggle="modal" data-bs-target="#actionModal">დეკლარაცია</button>
                                    </td>
                                    <!-- Add other columns -->
                                </tr>
                            `);
                        });

                        // Update pagination
                        $('#pagination').html(response.pagination);
                        console.log('Pagination HTML updated:', response.pagination); // Debug
                        // JavaScript function to handle the redirect
                        function redirectToEditPage(event, id) {
                            // Get the button element that was clicked
                            const button = event.target;

                            // Replace the placeholder with the actual ID in the data-url attribute
                            const url = button.getAttribute('data-url').replace('ID_PLACEHOLDER', id);

                            // Redirect the user to the edit page
                            window.location.href = url;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', error); // Debug
                        console.error('Status:', status); // Debug
                        console.error('Response:', xhr.responseText); // Debug
                    }
                });
            }

            $(document).on('click', '.edit-btn', function() {
                let itemId = $(this).data('id');
                let url = $(this).data('url').replace('ID_PLACEHOLDER', itemId);

                // Redirect to the correct URL
                window.location.href = url;
            });
            $(document).on('click', '.delete-btn', function() {
                let itemId = $(this).data('id');
                let url = $(this).data('url').replace('ID_PLACEHOLDER', itemId);

                // SweetAlert2 confirmation dialog
                Swal.fire({
                    title: 'დარწმუნებული ხართ?',
                    text: 'ამის დაბრუნება შეუძლებელი იქნება!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'დიახ, წაშალე!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Redirect to the delete route
                        window.location.href = url;
                    }
                });
            });

            // Initial data load - check if page parameter exists in URL
            const initialPage = parseInt(getUrlParameter('page')) || 1;
            loadData(initialPage, 'id', 'asc', {}, 10, false); // false = don't update URL on initial load

            $('#search-small_comment, #search-flight_parcel_state, #search-is_payed, #search-is_declared, #search-must_customize, #search-uses_courier, #search-branch_id, #search-id-min, #search-id-max, #search-tracking-code, #search-rec_name, #search-room_number, #search-flight_number, #start-takeout_date, #end-takeout_date, #search-phisicalw, #search-price_to_pay, $search-admin_comment').on('change', function() {
                let searchData = {
                    id_min: $('#search-id-min').val(),
                    id_max: $('#search-id-max').val(),
                    tracking_code: $('#search-tracking-code').val(),
                    rec_name: $('#search-rec_name').val(),
                    room_number: $('#search-room_number').val(),
                    flight_number: $('#search-flight_number').val(),
                    start_takeout_date: $('#start-takeout_date').val(),
                    end_takeout_date: $('#end-takeout_date').val(),
                    phisicalw: $('#search-phisicalw').val(),
                    price_to_pay: $('#search-price_to_pay').val(),
                    small_comment: $('#search-small_comment').val(),
                    flight_parcel_state: $('#search-flight_parcel_state').val(),
                    is_payed: $('#search-is_payed').val(),
                    is_declared: $('#search-is_declared').val(),
                    must_customize: $('#search-must_customize').val(),
                    uses_courier: $('#search-uses_courier').val(),
                    branch_id: $('#search-branch_id').val(),
                    admin_comment: $('#search-admin_comment').val(),
                };

                let sortColumn = $(this).data('column');
                let sortDirection = $(this).hasClass('asc') ? 'desc' : 'asc';

                loadData(1, sortColumn, sortDirection, searchData, 10, true);
            });

            // Clear Filters
            $('#clear-filters').on('click', function () {
                // Reset all filters to their default state
                $('#search-small_comment, #search-flight_parcel_state, #search-is_payed, #search-is_declared, #search-must_customize, #search-uses_courier, #search-branch_id, #search-id-min, #search-id-max, #search-tracking-code, #search-rec_name, #search-room_number, #search-flight_number, #start-takeout_date, #end-takeout_date, #search-phisicalw, #search-price_to_pay, #search-admin_comment').val('');

                // Optionally trigger 'change' event if needed
                $('#search-small_comment, #search-flight_parcel_state, #search-is_payed, #search-is_declared, #search-must_customize, #search-uses_courier, #search-branch_id, #search-id-min, #search-id-max, #search-tracking-code, #search-rec_name, #search-room_number, #search-flight_number, #start-takeout_date, #end-takeout_date, #search-phisicalw, #search-price_to_pay, #search-admin_comment').change();

                // Reload data with empty filters
                loadData(1, undefined, undefined, {}, 10, true);
            });


            // Sorting for ID and Name columns (only on header click)
            $('.sortable').on('click', function () {
                if ($(this).find('input').is(":focus") || $(this).find('select').is(":focus")) {
                    return; // Ignore if the input or select box is focused
                }

                let sortColumn = $(this).data('column');
                let sortDirection = $(this).hasClass('asc') ? 'desc' : 'asc';

                // Reset all headers
                $('.sortable').removeClass('asc desc');
                $('.sort-icon').text(''); // Clear all sort icons

                // Toggle the current header's direction
                $(this).toggleClass('asc', sortDirection === 'asc').toggleClass('desc', sortDirection === 'desc');

                // Add the appropriate arrow
                const arrow = sortDirection === 'asc' ? '⬆️' : '⬇️';
                $(this).find('.sort-icon').text(arrow);

                let searchData = {
                    id_min: $('#search-id-min').val(),
                    id_max: $('#search-id-max').val(),
                    tracking_code: $('#search-tracking-code').val(),
                    rec_name: $('#search-rec_name').val(),
                    room_number: $('#search-room_number').val(),
                    flight_number: $('#search-flight_number').val(),
                    start_takeout_date: $('#start-takeout_date').val(),
                    end_takeout_date: $('#end-takeout_date').val(),
                    phisicalw: $('#search-phisicalw').val(),
                    price_to_pay: $('#search-price_to_pay').val(),
                    small_comment: $('#search-small_comment').val(),
                    flight_parcel_state: $('#search-flight_parcel_state').val(),
                    is_payed: $('#search-is_payed').val(),
                    is_declared: $('#search-is_declared').val(),
                    must_customize: $('#search-must_customize').val(),
                    uses_courier: $('#search-uses_courier').val(),
                    branch_id: $('#search-branch_id').val(),
                    admin_comment: $('#search-admin_comment').val(),
                };


                // Call loadData with the current sort state
                loadData(1, sortColumn, sortDirection, searchData, 10, true);
            });


            // Handle pagination click with more specific selector
            $(document).on('click', 'a[data-page]', function(e) {
                e.preventDefault();

                console.log('Pagination link clicked!', this); // Debug

                // Get page number from data-page attribute instead of href
                let page = $(this).attr('data-page');
                console.log('Page from data-page:', page); // Debug

                // Skip if no page data (for disabled links)
                if (!page) {
                    console.log('No page data found, returning'); // Debug
                    return;
                }

                let sort_column = $('.sortable.asc').data('column') || 'id';
                let sort_direction = $('.sortable.asc').length ? 'asc' : 'desc';

                // Get perPage value
                let perPage = $('#perPage').val();
                let searchData = {
                    id_min: $('#search-id-min').val(),
                    id_max: $('#search-id-max').val(),
                    tracking_code: $('#search-tracking-code').val(),
                    rec_name: $('#search-rec_name').val(),
                    room_number: $('#search-room_number').val(),
                    flight_number: $('#search-flight_number').val(),
                    start_takeout_date: $('#start-takeout_date').val(),
                    end_takeout_date: $('#end-takeout_date').val(),
                    phisicalw: $('#search-phisicalw').val(),
                    price_to_pay: $('#search-price_to_pay').val(),
                    small_comment: $('#search-small_comment').val(),
                    flight_parcel_state: $('#search-flight_parcel_state').val(),
                    is_payed: $('#search-is_payed').val(),
                    is_declared: $('#search-is_declared').val(),
                    must_customize: $('#search-must_customize').val(),
                    uses_courier: $('#search-uses_courier').val(),
                    branch_id: $('#search-branch_id').val(),
                    admin_comment: $('#search-admin_comment').val(),
                };

                console.log('Calling loadData with page:', page); // Debug
                // Call loadData with the current search, sort, perPage, and page parameters
                loadData(page, sort_column, sort_direction, searchData, perPage, true); // true = update URL
            });

            // Handle perPage change
            $('#perPage').on('change', function() {
                let perPage = $(this).val();
                loadData(1, undefined, undefined, {}, perPage, true);  // Reload data with new perPage value
            });
        });
    </script>
@endsection
