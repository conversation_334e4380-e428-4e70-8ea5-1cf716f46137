@extends('layouts.admin')
@section('modal')
    <div class="modal fade" id="excelModal" data-backdrop="static" tabindex="-1" role="dialog"
         aria-labelledby="staticBackdrop" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h3 class="modal-title">ამანათების იმპორტი</h3>
                    </div>
                    <div class="modal-body">
                        <form action="{{route("flights.excel")}}" class="" method="post" enctype="multipart/form-data"
                              novalidate="novalidate">
                            @csrf
                            <section>
                                <div class="alert alert-warning">
                                    <button class="close" data-dismiss="alert">
                                        ×
                                    </button>
                                    <span class="svg-icon svg-icon-white svg-icon-2x">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                                                viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24"/>
                                                    <path
                                                        d="M11.1669899,4.49941818 L2.82535718,19.5143571 C2.557144,19.9971408 2.7310878,20.6059441 3.21387153,20.8741573 C3.36242953,20.9566895 3.52957021,21 3.69951446,21 L21.2169432,21 C21.7692279,21 22.2169432,20.5522847 22.2169432,20 C22.2169432,19.8159952 22.1661743,19.6355579 22.070225,19.47855 L12.894429,4.4636111 C12.6064401,3.99235656 11.9909517,3.84379039 11.5196972,4.13177928 C11.3723594,4.22181902 11.2508468,4.34847583 11.1669899,4.49941818 Z"
                                                        fill="#000000" opacity="0.3"/>
                                                    <rect fill="#000000" x="11" y="9" width="2" height="7" rx="1"/>
                                                    <rect fill="#000000" x="11" y="17" width="2" height="2" rx="1"/>
                                                </g>
                                            </svg><!--end::Svg Icon-->
                                        </span>
                                    <strong>ყურადღება</strong>
                                    იმპორტის შესასრულებლად საჭიროა Excel ფაილი '.xls' გაფართოებით.<br>
                                    ველების თანმიმდევრობა იხილეთ მოცემულ ცხრილში<br>
                                    ცხრილი არ უნდა მოიცავდეს სათაურებს!!!
                                </div>
                            </section>
                            <section>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tbody>
                                        <tr>
{{--                                            <td>თარიღი</td>--}}
                                            <td>სახელი გვარი</td>
                                            <td>თრექინგის კოდი</td>
                                            <td>ოთახის ნომერი</td>
                                            <td>წონა</td>
                                        </tr>
                                        <tr>
{{--                                            <td>31/10/2017</td>--}}
                                            <td>Onise rijamadze</td>
                                            <td>457582229473</td>
                                            <td>GDX01036</td>
                                            <td>1.1</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </section>
                            <input type="hidden" name="flight_id" id="flight_id">
                            <input type="file" id="excel" name="excel" style="visibility:hidden">
                            <section class="col col-md-6">
                                <a href="javascript:void(0);" onclick="$('#excel').click()"
                                   class="upload-btn btn btn-default" style="margin: 0">იმპორტი</a>
                                <button class="btn btn-light-primary ">Go!</button>

                            </section>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">დახურვა</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{--    washlis double check--}}
    <div class="modal fade" id="DeleteModal" tabindex="-1" role="dialog" aria-labelledby="DeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title geo" id="DeleteModalLabel ">ქალაქის წაშლა</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="form" id="kt_form" action="{{route("cashflow.delete2")}}" method="POST" >
                        @csrf
                        <input type="hidden" name="cashflow_id" id="cashflow_id">
                        <div class="form-group col-12 geo">
                            <label>ნამდვილად გსურთ ქალაქის წაშლა?</label>

                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary geo" data-dismiss="modal">დახურვა</button>
                            <button type="submit" class="btn btn-primary geo declaration_button">დადასტურება</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
@endsection
@section('content')



    <div class="d-flex flex-column-fluid">
        <div class=" container ">
            <div class="card card-custom">
                <div class="card-header flex-wrap border-0 pt-6 pb-0">
                    <div class="card-title">
                        <h3 class="card-label geo">Cashflow ცხრილი
                            <div class="text-muted pt-2 font-size-sm geo">ფინანსური ოპერაციების ცხრილი</div>
                        </h3>
                    </div>
                    <div class="card-toolbar">

                        <!--begin::Dropdown-->
                        <!--end::Dropdown-->
                        <!--begin::Button-->
                        <a href="{{route("cashflow.export.form")}}" class="btn btn-success font-weight-bolder mr-2">
                            <span class="svg-icon svg-icon-md">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"/>
                                        <path d="M3,16 L5,16 C5.55228475,16 6,15.5522847 6,15 C6,14.4477153 5.55228475,14 5,14 L3,14 L3,12 L5,12 C5.55228475,12 6,11.5522847 6,11 C6,10.4477153 5.55228475,10 5,10 L3,10 L3,8 L5,8 C5.55228475,8 6,7.55228475 6,7 C6,6.44771525 5.55228475,6 5,6 L3,6 L3,4 C3,3.44771525 3.44771525,3 4,3 L10,3 C10.5522847,3 11,3.44771525 11,4 L11,19 C11,19.5522847 10.5522847,20 10,20 L4,20 C3.44771525,20 3,19.5522847 3,19 L3,16 Z" fill="#000000" opacity="0.3"/>
                                        <path d="M16,3 L19,3 C20.1045695,3 21,3.8954305 21,5 L21,15.2485298 C21,15.7329761 20.8241635,16.200956 20.5051534,16.565539 L17.8762883,19.5699562 C17.6944473,19.7777745 17.378566,19.7988332 17.1707477,19.6169922 C17.1540423,19.602375 17.1383289,19.5866616 17.1237117,19.5699562 L14.4948466,16.565539 C14.1758365,16.200956 14,15.7329761 14,15.2485298 L14,5 C14,3.8954305 14.8954305,3 16,3 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </span>
                            ექსპორტი
                        </a>
                        <a href="{{route("cashflow.create")}}" class="btn btn-primary font-weight-bolder">
                            <span class="svg-icon svg-icon-md">
                                <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                                <svg xmlns="http://www.w3.org/2000/svg"
                                     xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                                     height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"/>
                                        <circle fill="#000000" cx="9" cy="15" r="6"/>
                                        <path
                                            d="M8.8012943,7.00241953 C9.83837775,5.20768121 11.7781543,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,12.2218457 18.7923188,14.1616223 16.9975805,15.1987057 C16.9991904,15.1326658 17,15.0664274 17,15 C17,10.581722 13.418278,7 9,7 C8.93357256,7 8.86733422,7.00080962 8.8012943,7.00241953 Z"
                                            fill="#000000" opacity="0.3"/>
                                    </g>
                                </svg>
                                <!--end::Svg Icon-->
                            </span>
                            New Record
                        </a>
                        <!--end::Button-->
                    </div>
                </div>

                <div class="card-body">
                    <!--begin: Search Form-->
                    <!--begin::Search Form-->

                    <div class="mb-7">
                        <div class="row align-items-center">
                            <div class="col-lg-10 col-xl-10">
                                <div class="row align-items-center">
                                    <div class="col my-2 my-md-0">
                                            <label class="">ძებნა:</label>
                                        <div class="input-icon">
                                            <input type="text" class="form-control" name="search" placeholder="ძებნა..."
                                                   id="kt_datatable_search_query" />
                                            <span>
                                                <i class="flaticon2-search-1 text-muted"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col my-2 my-md-0">
                                        <div class="form-group">
                                            <label class="">სტატუსი:</label>
                                            <select class="form-control" id="status_filter">
                                                <option value="">ყველა</option>
                                                <option value="1">შემოსავალი</option>
                                                <option value="0">გასავალი</option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="col my-2 my-md-0">
                                        <div class="form-group">
                                            <label class="">ფილიალი:</label>
                                            <select class="form-control" id="branch_filter">
                                                <option value="">ყველა</option>
                                                @foreach($branches as $branch)
                                                    <option value="{{$branch->id}}">{{$branch->title_ge}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col my-2 my-md-0">
                                        <div class="form-group">
                                            <label class="">თარიღიდან:</label>
                                            <input type="date" class="form-control" id="start_date_filter">
                                        </div>
                                    </div>
                                    <div class="col my-2 my-md-0">
                                        <div class="form-group">
                                            <label class="">თარიღამდე:</label>
                                            <input type="date" class="form-control" id="end_date_filter">
                                        </div>
                                    </div>
                                    <div class="col my-2 my-md-0">
                                        <div class="form-group">
                                            <label class="">გადახდის წყარო:</label>
                                            <select class="form-control" id="type_filter">
                                                <option value="">ყველა</option>
                                                <option value="terminal">ტერმინალი</option>
                                                <option value="tbc">TBC</option>
                                                <option value="bog">BOG</option>
                                            </select>
                                        </div>
                                    </div>

                                </div>
                            </div>
{{--                            <div class="col-lg-2 col-xl-2 mt-5 mt-lg-0">--}}
{{--                                <a href="#" class="btn btn-light-primary px-6 font-weight-bold w-100 geo">ძიება</a>--}}
{{--                            </div>--}}
                        </div>
                    </div>
                    <div class="mb-7">
                        <div class="row align-items-center">
                            <div class="col-lg-10 col-xl-10">
                                <div class="row align-items-center">
                                    <div class="col my-2 my-md-0">
                                        <div class="d-flex align-items-center">
                                            <span class="font-weight-boldest mr-2">ჯამური თანხა:</span>
                                            <span id="total-amount" class="font-weight-boldest">0 ლარი</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Search Form-->
                    <!--end: Search Form-->
                    <!--------------------------------begin: Datatable------------------------------------------------------------------------------>

                    <div class="geo datatable datatable-bordered datatable-head-custom" id="kt_datatable"></div>

                </div>
            </div>
        </div>
    </div>

@endsection
@section("scripts")
    <script>
        "use strict";
        // Class definition

        var KTDatatableRemoteAjaxDemo = function () {
            // Private functions

            // basic demo
            var demo = function () {

                var datatable = $('#kt_datatable').KTDatatable({
                    // datasource definition
                    data: {
                        type: 'remote',
                        source: {
                            read: {
                                url: '{{route("cashflow.paginate2")}}',
                                // sample custom headers
                                headers: {
                                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                },
                                map: function (raw) {
                                    // sample data mapping
                                    var dataSet = raw;
                                    if (typeof raw.data !== 'undefined') {
                                        dataSet = raw.data;
                                        $('#total-amount').text(raw.totalAmount + ' ლარი');
                                    }
                                    return dataSet;
                                },
                            },
                        },
                        pageSize: 10,
                        serverPaging: true,
                        serverFiltering: true,
                        serverSorting: true,
                    },

                    // layout definition
                    layout: {
                        scroll: false,
                        footer: false,
                    },

                    // column sorting
                    sortable: true,

                    pagination: true,

                    search: {
                        input: $('#kt_datatable_search_query'),
                        key: 'generalSearch'
                    },
                    query: {
                        branch_id: function() {
                            return $('#branch_filter').val();
                        },
                        start_date: function() {
                            return $('#start_date_filter').val();
                        },
                        end_date: function() {
                            return $('#end_date_filter').val();
                        },
                        type_filter: function() {
                            return $('#type_filter').val();
                        }
                    },

                    // columns definition
                    columns: [
                        {
                        field: 'invoice_id',
                        title: 'invoice_id',
                        sortable: 'desc',
                        type: 'string',
                        selector: false,
                        textAlign: 'center',
                    },
                        {
                            field: 'identification',
                            title: 'პირადი ნომერი',
                            // callback function support for column rendering
                            template: function (row) {
                                return row.user.identification;
                            },
                        },
                        {
                            field: 'user_room_code',
                            title: 'მომხმარებელი',
                            // callback function support for column rendering
                            template: function (row) {
                                return row.user.user_room_code;
                            },
                        },
                        {
                            field: 'email',
                            title: 'email',
                            // callback function support for column rendering
                            template: function (row) {
                                return row.user.email;
                            },
                        },

                        {
                            field: 'user',
                            title: 'მომხმარებელი',
                            // callback function support for column rendering
                            template: function (row) {
                                return row.user.first_name_ge+' '+row.user.last_name_ge;
                            },
                        },

                        {
                        field: 'amount',
                        title: 'თანხა',
                    },
                        // {
                        //     field: 'amount',
                        //     title: 'თანხა',
                        //     width: 50,
                        //     template: function(row) {
                        //
                        //         return parseFloat(row.amount).toFixed(2);
                        //     },
                        // },


                        {
                        field: 'is_income',
                        title: 'სტატუსი',
                        // callback function support for column rendering
                        template: function (row) {
                            var status = {
                                "0": {
                                    'title': 'გასავალი',
                                    'class': ' label-light-danger'
                                },
                                "1": {
                                    'title': 'შემოსავალი',
                                    'class': ' label-light-success'
                                },

                            };
                            return '<span class="label font-weight-bold label-lg ' + status[row.is_income].class + ' label-inline">' + status[row.is_income].title + '</span>';
                        },
                    },
                        {
                            field: 'payment_source',
                            title: 'გადახდის წყარო',
                            // callback function support for column rendering
                            template: function (row) {
                                if (row.payment_source === 'bog') {
                                    return '<span class="label font-weight-bold label-lg label-light-warning label-inline">BOG</span>';
                                } else if (row.payment_source === 'tbc') {
                                    return '<span class="label font-weight-bold label-lg label-light-primary label-inline">TBC</span>';
                                } else if (row.payment_source === 'terminal') {
                                    return '<span class="label font-weight-bold label-lg label-light-success label-inline">ტერმინალი</span>';
                                } else if (row.payment_source === 'TBC-ტერმინალი') {
                                    return '<span class="label font-weight-bold label-lg label-light-primary label-inline">TBC-ტერმინალი</span>';
                                } else if (row.payment_source === 'BOG-ტერმინალი') {
                                    return '<span class="label font-weight-bold label-lg label-light-warning label-inline">BOG-ტერმინალი</span>';
                                } else if (row.payment_source === null || row.payment_source === undefined || row.payment_source === '') {
                                    return '<span class="label font-weight-bold label-lg label-light-info label-inline">ამოუცნობი</span>';
                                } else {
                                    return '<span class="label font-weight-bold label-lg label-light-secondary label-inline">-</span>';
                                }
                            },
                        },
                        {
                            field: 'created_at',
                            title: 'შექმნის თარიღი',
                            template: function (row) {


                                return new Date(row.created_at).toLocaleString();
                            },
                        },






                        {
                            field: 'Actions',
                            title: 'Actions',
                            sortable: false,
                            width: 125,
                            overflow: 'visible',
                            autoHide: false,
                            template: function (row) {
                                return '' +
                                    '<a href="/admin/cashflow/edit/' + row.id + '" class="btn btn-sm btn-clean btn-icon mr-2" title="Edit details">' +
                                    '<span class="svg-icon svg-icon-md">' +
                                    '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">' +
                                    '    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">' +
                                    '        <rect x="0" y="0" width="24" height="24"/>' +
                                    '        <path d="M8,17.9148182 L8,5.96685884 C8,5.56391781 8.16211443,5.17792052 8.44982609,4.89581508 L10.965708,2.42895648 C11.5426798,1.86322723 12.4640974,1.85620921 13.0496196,2.41308426 L15.5337377,4.77566479 C15.8314604,5.0588212 16,5.45170806 16,5.86258077 L16,17.9148182 C16,18.7432453 15.3284271,19.4148182 14.5,19.4148182 L9.5,19.4148182 C8.67157288,19.4148182 8,18.7432453 8,17.9148182 Z" fill="#000000" fill-rule="nonzero"\ transform="translate(12.000000, 10.707409) rotate(-135.000000) translate(-12.000000, -10.707409) "/>' +
                                    '        <rect fill="#000000" opacity="0.3" x="5" y="20" width="15" height="2" rx="1"/>' +
                                    '    </g>' +
                                    '</svg>' +
                                    '</span>' +
                                    '</a>' +
                                    //invoice
                                    '<a href="/admin/cashflow/invoice/' + row.invoice_id + '" class="  btn btn-sm btn-clean btn-icon mr-2" title="Edit details">' +
                                    '<span class="svg-icon svg-icon-md">' +
                                    '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">' +
                                        '<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">' +
                                        '<polygon points="0 0 24 0 24 24 0 24" />' +
                                        '<path d="M4.85714286,1 L11.7364114,1 C12.0910962,1 12.4343066,1.12568431 12.7051108,1.35473959 L17.4686994,5.3839416 C17.8056532,5.66894833 18,6.08787823 18,6.52920201 L18,19.0833333 C18,20.8738751 17.9795521,21 16.1428571,21 L4.85714286,21 C3.02044787,21 3,20.8738751 3,19.0833333 L3,2.91666667 C3,1.12612489 3.02044787,1 4.85714286,1 Z M8,12 C7.44771525,12 7,12.4477153 7,13 C7,13.5522847 7.44771525,14 8,14 L15,14 C15.5522847,14 16,13.5522847 16,13 C16,12.4477153 15.5522847,12 15,12 L8,12 Z M8,16 C7.44771525,16 7,16.4477153 7,17 C7,17.5522847 7.44771525,18 8,18 L11,18 C11.5522847,18 12,17.5522847 12,17 C12,16.4477153 11.5522847,16 11,16 L8,16 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />' +
                                        '<path d="M6.85714286,3 L14.7364114,3 C15.0910962,3 15.4343066,3.12568431 15.7051108,3.35473959 L20.4686994,7.3839416 C20.8056532,7.66894833 21,8.08787823 21,8.52920201 L21,21.0833333 C21,22.8738751 20.9795521,23 19.1428571,23 L6.85714286,23 C5.02044787,23 5,22.8738751 5,21.0833333 L5,4.91666667 C5,3.12612489 5.02044787,3 6.85714286,3 Z M8,12 C7.44771525,12 7,12.4477153 7,13 C7,13.5522847 7.44771525,14 8,14 L15,14 C15.5522847,14 16,13.5522847 16,13 C16,12.4477153 15.5522847,12 15,12 L8,12 Z M8,16 C7.44771525,16 7,16.4477153 7,17 C7,17.5522847 7.44771525,18 8,18 L11,18 C11.5522847,18 12,17.5522847 12,17 C12,16.4477153 11.5522847,16 11,16 L8,16 Z" fill="#000000" fill-rule="nonzero" />' +
                                        '</g>' +
                                    '</svg>' +
                                    '</span>' +
                                    '</a>' +
                                    //delete
                                    // '<a href="/admin/cashflow/delete/' + row.id + '" class="btn btn-sm btn-clean btn-icon" title="Delete">' +
                                    // '<span class="svg-icon svg-icon-md">' +
                                    // '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">' +
                                    // '    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">' +
                                    // '        <rect x="0" y="0" width="24" height="24"/>' +
                                    // '        <path d="M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z" fill="#000000" fill-rule="nonzero"/>' +
                                    // '        <path d="M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z" fill="#000000" opacity="0.3"/>' +
                                    // '    </g>' +
                                    // '</svg>' +
                                    // '</span>' +
                                    // '</a>';
                                    // begin delete
                                    '<button  class="btn btn-sm btn-clean btn-icon btn-delete"  ' +
                                    'data-toggle="modal" data-target="#DeleteModal" data-id="' + row.id + '" title="ფრენის წაშლა">' +
                                    '<span class="svg-icon svg-icon-md">' +
                                    '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">' +
                                    '    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">' +
                                    '        <rect x="0" y="0" width="24" height="24"/>' +
                                    '        <path d="M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z" fill="#000000" fill-rule="nonzero"/>' +
                                    '        <path d="M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z" fill="#000000" opacity="0.3"/>' +
                                    '    </g>' +
                                    '</svg>' +
                                    '</span>' +
                                    '</button>';

                            },
                        }],

                });

                // $('#kt_datatable_search_status').on('change', function () {
                //     datatable.search($(this).val().toLowerCase(), 'Status');
                // });
                //
                // $('#kt_datatable_search_type').on('change', function () {
                //     datatable.search($(this).val().toLowerCase(), 'Type');
                // });

                $('#kt_datatable_search_status, #kt_datatable_search_type').selectpicker();
                // new code
                $('#kt_datatable_search_query').on('change', function() {
                    datatable.search($(this).val(), 'search');
                });

                $('#status_filter, #branch_filter, #start_date_filter, #end_date_filter, #type_filter').on('change', function() {
                    datatable.search($(this).val().toLowerCase(), $(this).attr('id').replace('_filter', ''));
                });
            };

            return {
                // public functions
                init: function () {
                    demo();
                },
            };
        }();

        jQuery(document).ready(function () {
            KTDatatableRemoteAjaxDemo.init();

            $(document).on("click",".excel-import",function() {
                let flight_id = $(this).data("id");
                $("#flight_id").val(flight_id);
            });
        });
        $(document).on("click",".btn-delete",function() {
            let cashflow_id = $(this).data("id");
            $("#cashflow_id").val(cashflow_id);
        });

    </script>
@endsection
