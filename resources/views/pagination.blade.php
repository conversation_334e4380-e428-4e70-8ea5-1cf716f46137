@if ($paginator->hasPages())
    <div class="d-flex justify-content-between align-items-center mb-3">
        {{-- Calculate the start and end record numbers --}}
        @php
            $start = ($paginator->currentPage() - 1) * $paginator->perPage() + 1;
            $end = min($paginator->currentPage() * $paginator->perPage(), $paginator->total());
        @endphp
        <p>Showing {{ $start }} - {{ $end }} of {{ $paginator->total() }}</p>

    </div>
    <ul class="pagination">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <li class="page-item disabled"><span class="page-link">Previous</span></li>
        @else
            <li class="page-item"><a class="page-link" href="javascript:void(0)" data-page="{{ $paginator->currentPage() - 1 }}">Previous</a></li>
        @endif

        {{-- First Page Link --}}
        @if ($paginator->currentPage() > 3)
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" data-page="1">1</a>
            </li>
            {{-- Ellipsis after first page link --}}
            <li class="page-item disabled"><span class="page-link">...</span></li>
        @endif

        {{-- Page Number Links with Ellipses --}}
        @php
            $start = max(1, $paginator->currentPage() - 2); // Start from current page minus 2
            $end = min($start + 4, $paginator->lastPage()); // Limit to last page
        @endphp

        @for ($page = $start; $page <= $end; $page++)
            <li class="page-item {{ $paginator->currentPage() == $page ? 'active' : '' }}">
                <a class="page-link" href="javascript:void(0)" data-page="{{ $page }}">{{ $page }}</a>
            </li>
        @endfor

        {{-- Ellipses when there are pages after the visible ones --}}
        @if ($end < $paginator->lastPage() - 1)
            <li class="page-item disabled"><span class="page-link">...</span></li>
        @endif

        {{-- Last Page Link as a numeric link --}}
        @if ($paginator->currentPage() < $paginator->lastPage() - 1)
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" data-page="{{ $paginator->lastPage() }}">{{ $paginator->lastPage() }}</a>
            </li>
        @endif

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <li class="page-item"><a class="page-link" href="javascript:void(0)" data-page="{{ $paginator->currentPage() + 1 }}">Next</a></li>
        @else
            <li class="page-item disabled"><span class="page-link">Next</span></li>
        @endif


    </ul>
@endif
