<?php

namespace App\Http\Controllers;

use App\Exports\CashflowExport;
use App\Models\Branch;
use App\Models\Cashflow;
use App\Models\goods;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Maatwebsite\Excel\Facades\Excel;

class CashflowController extends Controller
{
    //
    //
    public function index()
    {
        $branches = Branch::all();
        return view('admin.cashflow.index', compact('branches'));
    }

    public function create()
    {

        $users = User::all();
        return view('admin.cashflow.create', compact('users'));
    }
//  ---------------  query scope
// Add scope methods in model class


// Controller
// ---------------------   query scope



    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|numeric',
            'amount' => 'required',

        ]);




        $is_income = null;
        $amount = null;
        if($request->input('amount')>0)
        {
            $is_income = true;
            $amount = $request->input('amount');

        } else {
            $is_income = false;
            $amount = -1 * abs($request->input('amount'));
        }

        $cashflow = Cashflow::create([
            "user_id" => $request->input('user_id'),
            "amount" => $amount,
            "is_income"=> $is_income,
            "terminal" => $request->input('terminal', false),
            "payment_source" => $request->input('payment_source') ?: ($request->input('terminal', false) ? "terminal" : null)
        ]);
        $cashflow->save();

        //user is balance i cvlileba
        $user = User:: where("id", $cashflow->user_id)->first();
        $user->balance = $user->balance + $cashflow->amount;
//        $user->balance = $user->balance + (-1 * abs($cashflow->amount));
        $user->save();


        //       pay from balance first find user
        $user_id2 = $request->input('user_id');
        //        goods that must be payed.
        $goods1 = goods::where(["user_id"=>$user_id2,"is_payed"=>0])->get();
        foreach ($goods1 as $good1) {
            if ($user->balance>=0) {
                $good1->is_payed = 1;
                $good1->save();
            }
            else{

            }
        }
        return redirect()->route("cashflow.index")->with("success", "თანხა წარმატებით დაემატა.");
    }

    public function edit(cashflow $cashflow)
    {
//        $cashflow = Cashflow::all();
        $users = User::all();
        return view("admin.cashflow.edit", compact("users", "cashflow"));
    }

    public function update(cashflow $cashflow, Request $request)
    {
        $cashflow->update([
            "user_id" => $request->input('user_id'),
            "amount" => $request->input('amount'),
            "terminal" => $request->has('terminal'),
            "payment_source" => $request->input('payment_source')
        ]);
        return redirect()->route("cashflow.index",  $cashflow->id)->with("success", "ტრანზაქცია წარმატებით დარექაქტირდა.");
    }

    public function delete(cashflow $cashflow)
    {
        $is_income = null;
        $amount = null;
        if($cashflow->amount>0)
        {
            $is_income = true;
            $amount = $cashflow->amount;
        } else {
            $is_income = false;
            $amount = -1 * abs($cashflow->amount);
        }

        //user is balance i cvlileba
        $user = User:: where("id", $cashflow->user_id)->first();
        $user->balance = $user->balance - $amount;
        $user->save();

        $cashflow->delete();

        return redirect()->route("cashflow.index", $cashflow->id)->with("success", "ტრანზაქცია წარმატებით წაიშალა.");
    }

    public function delete2(Request $request)
    {
        //მეორე დელეიტი არის მოდალიდან წაშლის შემთხვევაში

        $id = $request->input('cashflow_id');
        $cashflow = Cashflow::find($id);


        //
        $is_income = null;
        $amount = null;
        if($cashflow->amount>0)
        {
            $is_income = true;
            $amount = $cashflow->amount;
        } else {
            $is_income = false;
            $amount = -1 * abs($cashflow->amount);
        }

        //user is balance i cvlileba
        $user = User:: where("id", $cashflow->user_id)->first();
        $user->balance = $user->balance - $amount;
        $user->save();


        $cashflow->delete();
        return redirect()->route("cashflow.index", $cashflow->id)->with("success", "თანხა წარმატებით წაიშალა.");
    }




    public function paginate(Request $request)
    {
        $currentPage = $request->input("pagination")['page'];
        // Make sure that you call the static method currentPageResolver()
        // before querying users
        Paginator::currentPageResolver(function () use ($currentPage) {
            return $currentPage;
        });
        $search = $request->input("query")['generalSearch'] ?? null;
        $status = $request->input("query")['status'] ?? null;

        $cashflow = Cashflow::with('user')
            ->when($search, function ($query, $searchQuery) {
            return $query->where("invoice_id", "like", "%".$searchQuery."%")
                ->orWhere("identification","like", "%".$searchQuery."%")
                ->orWhere("user_room_code","like", "%".$searchQuery."%")
                ->orWhere("email","like", "%".$searchQuery."%")
                ->orWhere("username","like", "%".$searchQuery."%")
                ->orWhere("amount","like", "%".$searchQuery."%");
             })
//            ->when($status, function ($query, $searchQuery) {
//                return $query->where("flight_parcel_state", $searchQuery);
//            })

            ->when($status, function ($query, $searchQuery) {
        return $query->where("is_income", $searchQuery);
    })
            ->orderBy('id', 'desc')
            ->paginate(25);

        $custom = collect([
            "meta" => [
                "page" => $cashflow->currentPage(),
                "pages" => $cashflow->lastPage(),
                "perpage" => $cashflow->perPage(),
                "total" => $cashflow->total(),
                "sort" => "desc",
                "field" => "id"
            ]
        ]);

        $data = $custom->merge($cashflow);

        return response()->json($data);
    }

    public function paginate2(Request $request)
    {
        $currentPage = $request->input("pagination")['page'];
        // Make sure that you call the static method currentPageResolver()
        // before querying users
        Paginator::currentPageResolver(function () use ($currentPage) {
            return $currentPage;
        });


        $search = $request->input("query")['generalSearch'] ?? null;
//        $status = $request->input("query")['status_filter'] ?? null;
        $status = $request->input("query")['status'] ?? null;
        $branch_id = $request->input("query")['branch'] ?? null;
        $start_date = $request->input("query")['start_date'] ?? null;
        $end_date = $request->input("query")['end_date'] ?? null;
        $paymentSource = $request->input("query")['type'] ?? null;

        $query = Cashflow::with(['user'])->whereHas('user', function ($query) use ($search) {
            if (!empty($search)) {
                $query->where('user_room_code', "like", "%" . $search . "%")
                    ->orWhere("identification", "like", "%" . $search . "%")
                    ->orWhere("email", "like", "%" . $search . "%")
                    ->orWhere("first_name_en", "like", "%" . $search . "%")
                    ->orWhere("last_name_en","like", "%". $search ."%")
                    ->orWhere("last_name_ge","like", "%". $search ."%")
                    ->orWhere("first_name_ge","like", "%". $search ."%");
            }
        });

        if (!empty($branch_id)) {
            $query = $query->whereHas('user', function ($query) use ($branch_id) {
                $query->where('branch_id', $branch_id);
            });
        }

        $query
            ->when($start_date, function ($query, $value) {
                $query->whereDate('created_at', '>=', $value);
            })
            ->when($end_date, function ($query, $value) {
                $query->whereDate('created_at', '<=', $value);
            })
            ->when($paymentSource, function ($query, $value){
                $query->where('payment_source', $value);
            })
        ;

//        if (!empty($status)) {
//            $query = $query->where("flight_parcel_state", $status);
//        }

        if (!empty($status)) {
            if ($status == "1") {
                $query = $query->where("is_income", 1);
            } else {
                $query = $query->where("is_income", 0);
            }
        }



        $parcels = $query
            ->orderBy('id', 'desc')
            ->paginate(50);
//        dd($parcels);

        $custom = collect([
            "meta" => [
                "page" => $parcels->currentPage(),
                "pages" => $parcels->lastPage(),
                "perpage" => $parcels->perPage(),
                "total" => $parcels->total(),
                "sort" => "desc",
                "field" => "flight_number"
            ]
        ]);

        $totalAmount = $query->sum('amount');

        $data = $custom->merge($parcels);
        $data->put('totalAmount', $totalAmount);

        return response()->json($data);

    }






    public function index2()
    {
//        $flights = Flights::all();
//        return view('admin.Flights', ['flights' => $flights]);


        $cashflow = Cashflow::orderBy("id", "desc")->get()->toArray();
//    dd($flights);
        return view('admin.cashflow.index2', compact('cashflow'));
    }



    public function invoice(Invoice $invoice)
    {
        $user = $invoice->good->user;
//        $user = Auth::user();

//        dd($user);
        return view('admin.cashflow.invoice',compact("invoice",'user'));
    }

    /**
     * Export cashflow data to Excel
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $branchId = $request->input('branch_id');

        return Excel::download(
            new CashflowExport($startDate, $endDate, $branchId),
            'cashflow_' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Show the export form
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function exportForm()
    {
        $branches = Branch::all();
        return view('admin.cashflow.export', compact('branches'));
    }
}
